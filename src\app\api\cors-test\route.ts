import { NextRequest, NextResponse } from 'next/server';

/**
 * 更精确的CORS测试API
 */
export async function GET(request: NextRequest) {
  const backendUrl = process.env.API_PROXY_TARGET || 'http://127.0.0.1:9999';
  
  const tests = [
    {
      name: '基础连接测试',
      url: `${backendUrl}/api/health`,
      description: '测试后端服务器是否运行'
    },
    {
      name: '认证端点测试', 
      url: `${backendUrl}/api/auth/profile`,
      description: '测试认证相关API'
    },
    {
      name: '资源统计测试',
      url: `${backendUrl}/api/resource_stats`, 
      description: '测试资源统计API'
    }
  ];

  const results = [];

  for (const test of tests) {
    try {
      console.log(`🧪 测试: ${test.name} -> ${test.url}`);
      
      const startTime = Date.now();
      const response = await fetch(test.url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'NextJS-Proxy-Test',
        },
        // 设置较短的超时时间
        signal: AbortSignal.timeout(5000)
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      let responseData = null;
      let responseText = '';
      
      try {
        responseText = await response.text();
        responseData = JSON.parse(responseText);
      } catch {
        responseData = responseText;
      }

      results.push({
        test: test.name,
        url: test.url,
        status: response.status,
        statusText: response.statusText,
        responseTime: `${responseTime}ms`,
        success: response.status < 500, // 4xx是正常的业务错误，5xx才是服务器错误
        headers: Object.fromEntries(response.headers.entries()),
        data: responseData,
        description: test.description
      });

      console.log(`✅ ${test.name}: ${response.status} (${responseTime}ms)`);
      
    } catch (error) {
      console.log(`❌ ${test.name}: ${error}`);
      
      results.push({
        test: test.name,
        url: test.url,
        status: 0,
        statusText: 'Network Error',
        responseTime: 'timeout',
        success: false,
        error: error instanceof Error ? error.message : String(error),
        description: test.description
      });
    }
  }

  // 分析结果
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  const analysis = {
    corsFixed: successCount > 0, // 如果有任何成功的响应，说明CORS已修复
    backendRunning: results.some(r => r.status > 0), // 如果有任何HTTP响应，说明后端在运行
    proxyWorking: true, // 如果能执行到这里，说明代理在工作
    summary: `${successCount}/${totalCount} 测试通过`
  };

  return NextResponse.json({
    timestamp: new Date().toISOString(),
    backend_url: backendUrl,
    analysis,
    results,
    recommendations: generateRecommendations(results)
  });
}

function generateRecommendations(results: any[]) {
  const recommendations = [];
  
  const hasNetworkErrors = results.some(r => r.status === 0);
  const has5xxErrors = results.some(r => r.status >= 500);
  const has4xxErrors = results.some(r => r.status >= 400 && r.status < 500);
  
  if (hasNetworkErrors) {
    recommendations.push('🔴 后端服务器可能没有运行，请检查 127.0.0.1:9999');
  }
  
  if (has5xxErrors) {
    recommendations.push('🟡 后端服务器运行但有内部错误');
  }
  
  if (has4xxErrors && !hasNetworkErrors && !has5xxErrors) {
    recommendations.push('🟢 CORS已修复，代理正常工作，只需要正确的认证');
  }
  
  if (results.every(r => r.success)) {
    recommendations.push('🎉 所有测试通过，系统运行正常！');
  }
  
  return recommendations;
}
