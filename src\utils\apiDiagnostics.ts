/**
 * API连接诊断工具
 * 用于检测和修复"Failed to fetch"错误
 */

// 获取API基础URL
const getApiBaseUrl = () => {
  return process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";
};

export interface DiagnosticResult {
  success: boolean;
  message: string;
  details?: any;
  error?: string;
}

export interface ApiEndpointTest {
  name: string;
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  requiresAuth: boolean;
  testData?: any;
}

/**
 * 测试API基础连接
 */
export async function testApiConnection(): Promise<DiagnosticResult> {
  const apiUrl = getApiBaseUrl();
  
  try {
    console.log('🔍 测试API基础连接:', apiUrl);
    
    // 测试基础连接
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    return {
      success: true,
      message: `API服务器连接成功 (${response.status})`,
      details: {
        url: apiUrl,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
      }
    };
  } catch (error) {
    console.error('❌ API连接失败:', error);
    return {
      success: false,
      message: 'API服务器连接失败',
      error: error instanceof Error ? error.message : '未知错误',
      details: {
        url: apiUrl,
        errorType: error instanceof TypeError ? 'Network Error' : 'Unknown Error',
      }
    };
  }
}

/**
 * 测试认证相关的API端点
 */
export async function testAuthEndpoints(): Promise<DiagnosticResult[]> {
  const endpoints: ApiEndpointTest[] = [
    {
      name: '用户登录',
      url: '/api/auth/login',
      method: 'POST',
      requiresAuth: false,
      testData: { username: 'test', password: 'test' }
    },
    {
      name: '用户注册',
      url: '/api/auth/register',
      method: 'POST',
      requiresAuth: false,
      testData: { username: 'test', email: '<EMAIL>', password: 'test' }
    },
    {
      name: '获取用户信息',
      url: '/api/auth/profile',
      method: 'GET',
      requiresAuth: true,
    },
    {
      name: '刷新令牌',
      url: '/api/auth/refresh',
      method: 'POST',
      requiresAuth: false,
      testData: { refresh_token: 'test' }
    },
    {
      name: '用户登出',
      url: '/api/auth/logout',
      method: 'POST',
      requiresAuth: true,
    }
  ];

  const results: DiagnosticResult[] = [];
  
  for (const endpoint of endpoints) {
    const result = await testSingleEndpoint(endpoint);
    results.push(result);
  }
  
  return results;
}

/**
 * 测试单个API端点
 */
async function testSingleEndpoint(endpoint: ApiEndpointTest): Promise<DiagnosticResult> {
  const fullUrl = `${getApiBaseUrl()}${endpoint.url}`;
  
  try {
    console.log(`🔍 测试端点: ${endpoint.name} (${endpoint.method} ${fullUrl})`);
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    // 如果需要认证，添加测试token
    if (endpoint.requiresAuth) {
      const token = localStorage.getItem('auth_token');
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      } else {
        return {
          success: false,
          message: `${endpoint.name}: 需要认证但未找到token`,
          details: { url: fullUrl, method: endpoint.method }
        };
      }
    }
    
    const requestOptions: RequestInit = {
      method: endpoint.method,
      headers,
    };
    
    if (endpoint.testData && (endpoint.method === 'POST' || endpoint.method === 'PUT')) {
      requestOptions.body = JSON.stringify(endpoint.testData);
    }
    
    const response = await fetch(fullUrl, requestOptions);
    
    // 尝试解析响应
    let responseData;
    try {
      responseData = await response.json();
    } catch {
      responseData = await response.text();
    }
    
    return {
      success: response.ok,
      message: `${endpoint.name}: ${response.ok ? '连接成功' : '连接失败'} (${response.status})`,
      details: {
        url: fullUrl,
        method: endpoint.method,
        status: response.status,
        statusText: response.statusText,
        responseData: typeof responseData === 'string' ? responseData.substring(0, 200) : responseData,
      }
    };
    
  } catch (error) {
    console.error(`❌ ${endpoint.name} 测试失败:`, error);
    return {
      success: false,
      message: `${endpoint.name}: 连接失败`,
      error: error instanceof Error ? error.message : '未知错误',
      details: {
        url: fullUrl,
        method: endpoint.method,
        errorType: error instanceof TypeError ? 'Network Error' : 'Unknown Error',
      }
    };
  }
}

/**
 * 检查网络连接状态
 */
export function checkNetworkStatus(): DiagnosticResult {
  if (typeof navigator !== 'undefined' && 'onLine' in navigator) {
    return {
      success: navigator.onLine,
      message: navigator.onLine ? '网络连接正常' : '网络连接断开',
      details: {
        onLine: navigator.onLine,
        userAgent: navigator.userAgent,
      }
    };
  }
  
  return {
    success: true,
    message: '无法检测网络状态（服务器端）',
    details: { environment: 'server' }
  };
}

/**
 * 检查环境变量配置
 */
export function checkEnvironmentConfig(): DiagnosticResult {
  const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
  const defaultUrl = 'http://localhost:8000';
  
  return {
    success: !!apiUrl,
    message: apiUrl ? `API URL已配置: ${apiUrl}` : `使用默认API URL: ${defaultUrl}`,
    details: {
      NEXT_PUBLIC_API_BASE_URL: apiUrl || '未设置',
      actualUrl: getApiBaseUrl(),
      isDefault: !apiUrl,
    }
  };
}

/**
 * 运行完整的API诊断
 */
export async function runFullDiagnostics(): Promise<{
  networkStatus: DiagnosticResult;
  environmentConfig: DiagnosticResult;
  apiConnection: DiagnosticResult;
  authEndpoints: DiagnosticResult[];
  summary: {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    criticalIssues: string[];
  };
}> {
  console.log('🚀 开始API诊断...');
  
  const networkStatus = checkNetworkStatus();
  const environmentConfig = checkEnvironmentConfig();
  const apiConnection = await testApiConnection();
  const authEndpoints = await testAuthEndpoints();
  
  const allTests = [networkStatus, environmentConfig, apiConnection, ...authEndpoints];
  const passedTests = allTests.filter(test => test.success).length;
  const failedTests = allTests.length - passedTests;
  
  const criticalIssues: string[] = [];
  if (!networkStatus.success) criticalIssues.push('网络连接问题');
  if (!apiConnection.success) criticalIssues.push('API服务器无法访问');
  if (authEndpoints.filter(test => test.success).length === 0) {
    criticalIssues.push('所有认证端点都无法访问');
  }
  
  console.log('✅ API诊断完成');
  
  return {
    networkStatus,
    environmentConfig,
    apiConnection,
    authEndpoints,
    summary: {
      totalTests: allTests.length,
      passedTests,
      failedTests,
      criticalIssues,
    }
  };
}
