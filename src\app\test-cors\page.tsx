'use client';

import { useState } from 'react';

/**
 * 测试CORS修复的页面
 */
export default function TestCorsPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // 测试代理连接
  const testProxy = async () => {
    setLoading(true);
    setResult('');

    try {
      const response = await fetch('/api/test-proxy');
      const data = await response.json();
      
      setResult(JSON.stringify(data, null, 2));
    } catch (error) {
      setResult(`请求失败: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  // 测试直接API调用（应该通过代理）
  const testDirectApi = async () => {
    setLoading(true);
    setResult('');

    try {
      const response = await fetch('/api/auth/profile', {
        headers: {
          'Authorization': 'Bearer test-token',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setResult(`API调用成功:\n${JSON.stringify(data, null, 2)}`);
      } else {
        const errorText = await response.text();
        setResult(`API调用失败 (${response.status}):\n${errorText}`);
      }
    } catch (error) {
      setResult(`API调用异常: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  // 测试资源统计API
  const testResourceStats = async () => {
    setLoading(true);
    setResult('');

    try {
      const response = await fetch('/api/resource_stats');
      
      if (response.ok) {
        const data = await response.json();
        setResult(`资源统计API成功:\n${JSON.stringify(data, null, 2)}`);
      } else {
        const errorText = await response.text();
        setResult(`资源统计API失败 (${response.status}):\n${errorText}`);
      }
    } catch (error) {
      setResult(`资源统计API异常: ${(error as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            CORS 修复测试页面
          </h1>

          <div className="space-y-4 mb-6">
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
              <h2 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">
                🔧 修复说明
              </h2>
              <ul className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
                <li>• 配置了Next.js API代理 (rewrites)</li>
                <li>• 客户端API调用使用相对路径</li>
                <li>• 代理目标: {process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:9999'}</li>
                <li>• 避免了直接跨域请求</li>
              </ul>
            </div>

            <div className="grid gap-4 md:grid-cols-3">
              <button
                onClick={testProxy}
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-4 rounded-md transition-colors"
              >
                {loading ? '测试中...' : '测试代理连接'}
              </button>

              <button
                onClick={testDirectApi}
                disabled={loading}
                className="w-full bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-medium py-3 px-4 rounded-md transition-colors"
              >
                {loading ? '测试中...' : '测试用户API'}
              </button>

              <button
                onClick={testResourceStats}
                disabled={loading}
                className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white font-medium py-3 px-4 rounded-md transition-colors"
              >
                {loading ? '测试中...' : '测试资源统计'}
              </button>
            </div>
          </div>

          {result && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                测试结果:
              </h3>
              <pre className="bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md p-4 text-sm text-gray-800 dark:text-gray-200 overflow-auto max-h-96">
                {result}
              </pre>
            </div>
          )}

          <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
            <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
              ⚠️ 注意事项
            </h3>
            <ul className="text-yellow-700 dark:text-yellow-300 text-sm space-y-1">
              <li>• 如果测试失败，请确保后端服务器正在运行</li>
              <li>• 后端服务器应该运行在 127.0.0.1:9999</li>
              <li>• 代理只在开发环境下有效</li>
              <li>• 生产环境需要配置服务器级别的代理或CORS</li>
            </ul>
          </div>

          <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
            <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-2">
              ✅ 预期结果
            </h3>
            <ul className="text-green-700 dark:text-green-300 text-sm space-y-1">
              <li>• 不再出现CORS错误</li>
              <li>• API请求通过Next.js代理转发</li>
              <li>• 用户认证功能正常工作</li>
              <li>• 页面加载时不会有跨域问题</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
