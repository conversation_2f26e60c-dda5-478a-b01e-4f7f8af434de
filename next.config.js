/** @type {import('next').NextConfig} */
const nextConfig = {
  // 优化webpack配置以减少ChunkLoadError
  webpack: (config, { dev, isServer }) => {
    // 在开发环境下优化chunk加载
    if (dev && !isServer) {
      // 减少代码分割，避免过多的动态chunk
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks.cacheGroups,
            // 将常用的工具函数打包到一个chunk中
            utils: {
              name: "utils",
              chunks: "all",
              test: /[\\/]src[\\/]utils[\\/]/,
              priority: 10,
              enforce: true,
            },
            // 将服务层打包到一个chunk中
            services: {
              name: "services",
              chunks: "all",
              test: /[\\/]src[\\/]services[\\/]/,
              priority: 10,
              enforce: true,
            },
          },
        },
      };

      // 移除babel-loader配置，使用Next.js默认的SWC编译器
    }

    return config;
  },

  // 实验性功能配置
  experimental: {
    // 启用更好的错误恢复
    optimizePackageImports: ["@/utils", "@/services"],
  },

  // 开发环境配置
  ...(process.env.NODE_ENV === "development" &&
    {
      // 开发环境下的特殊配置
      // swcMinify在Next.js 15中已被移除，使用默认配置
    }),
};

module.exports = nextConfig;
